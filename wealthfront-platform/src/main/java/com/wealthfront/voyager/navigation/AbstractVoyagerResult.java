package com.wealthfront.voyager.navigation;

import static com.kaching.api.ExposeTo.API_SERVER;
import static com.kaching.api.ExposeTo.BACKEND;
import static com.kaching.api.ExposeTo.LOCAL;
import static com.kaching.api.ExposeTo.TAOS;
import static com.kaching.api.ExposeType.RewriteNamespace.INLINE_INTO_CHILDREN;

import java.util.Locale;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.kaching.util.id.ExternalId;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.model.VoyagerType;
import com.wealthfront.voyager.ui.View;

@ExposeType(value = {BACKEND, TAOS, API_SERVER, LOCAL}, namespace = INLINE_INTO_CHILDREN)
@Entity
public abstract class AbstractVoyagerResult<V extends View> implements VoyagerResult<V> {

  @Value(nullable = false)
  private ExternalId<VoyagerRecord> voyagerRecordId;

  @Value(nullable = false)
  private VoyagerStepId stepId;

  @Value(nullable = false)
  private String stepPath;

  @Value(nullable = false)
  private VoyagerType voyagerType;

  @Value(nullable = false)
  private String pageViewMetricName;

  @Value(nullable = false)
  private String pageViewMetricCategory;

  public AbstractVoyagerResult() {}

  void setVoyagerMetadata(
      ExternalId<VoyagerRecord> voyagerRecordId,
      VoyagerStepId stepId,
      Option<String> stepPath,
      VoyagerType voyagerType) {
    this.voyagerRecordId = voyagerRecordId;
    this.stepId = stepId;
    this.stepPath = stepPath.getOrElse("");
    this.voyagerType = voyagerType;

    if (this.pageViewMetricName == null) {
      this.pageViewMetricName = stepPath.getOrElse("");
    }

    if (this.pageViewMetricCategory == null) {
      this.pageViewMetricCategory = voyagerType.name().toLowerCase(Locale.ROOT);
    }
  }

  @VisibleForTesting
  public void setStepIdForTesting(VoyagerStepId stepId) {
    this.stepId = stepId;
  }

  @VisibleForTesting
  public void setVoyagerRecordIdForTesting(ExternalId<VoyagerRecord> voyagerRecordId) {
    this.voyagerRecordId = voyagerRecordId;
  }

  @VisibleForTesting
  public void setStepPathForTesting(String stepPath) {
    this.stepPath = stepPath;
  }

  @VisibleForTesting
  public void setVoyagerTypeForTesting(VoyagerType voyagerType) {
    this.voyagerType = voyagerType;
  }

  @VisibleForTesting
  public void setPageViewMetricNameForTesting(String pageViewMetricName) {
    this.pageViewMetricName = pageViewMetricName;
  }

  @VisibleForTesting
  public void setPageViewMetricCategoryForTesting(String pageViewMetricCategory) {
    this.pageViewMetricCategory = pageViewMetricCategory;
  }

  @Override
  public VoyagerStepId getStepId() {
    return stepId;
  }

  @Override
  public ExternalId<VoyagerRecord> getVoyagerRecordId() {
    return voyagerRecordId;
  }

  @Override
  public String getStepPath() {
    return stepPath;
  }

  @Override
  public VoyagerType getVoyagerType() {
    return voyagerType;
  }

  @Override
  public String getPageViewMetricName() {
    return pageViewMetricName;
  }

  @Override
  public String getPageViewMetricCategory() {
    return pageViewMetricCategory;
  }

}
