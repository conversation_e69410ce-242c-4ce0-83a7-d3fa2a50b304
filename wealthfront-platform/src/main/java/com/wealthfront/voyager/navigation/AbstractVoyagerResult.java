package com.wealthfront.voyager.navigation;

import static com.kaching.api.ExposeTo.API_SERVER;
import static com.kaching.api.ExposeTo.BACKEND;
import static com.kaching.api.ExposeTo.LOCAL;
import static com.kaching.api.ExposeTo.TAOS;
import static com.kaching.api.ExposeType.RewriteNamespace.INLINE_INTO_CHILDREN;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.kaching.util.id.ExternalId;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.ui.View;

@ExposeType(value = {BACKEND, TAOS, API_SERVER, LOCAL}, namespace = INLINE_INTO_CHILDREN)
@Entity
public abstract class AbstractVoyagerResult<V extends View> implements VoyagerResult<V> {

  @Value(nullable = false)
  private ExternalId<VoyagerRecord> voyagerRecordId;

  @Value(nullable = false)
  private VoyagerStepId stepId;

  @Value(optional = true, nullable = true)
  private String stepPath;



  public AbstractVoyagerResult() { /* JSON */ }

  void setVoyagerMetadata(ExternalId<VoyagerRecord> voyagerRecordId, VoyagerStepId stepId, Option<String> stepPath) {
    this.voyagerRecordId = voyagerRecordId;
    this.stepId = stepId;
    this.stepPath = stepPath.getOrNull();
  }

  @VisibleForTesting
  public void setStepIdForTesting(VoyagerStepId stepId) {
    this.stepId = stepId;
  }

  @VisibleForTesting
  public void setVoyagerRecordIdForTesting(ExternalId<VoyagerRecord> voyagerRecordId) {
    this.voyagerRecordId = voyagerRecordId;
  }

  @VisibleForTesting
  public void setStepPathForTesting(String stepPath) {
    this.stepPath = stepPath;
  }

  @Override
  public VoyagerStepId getStepId() {
    return stepId;
  }

  @Override
  public ExternalId<VoyagerRecord> getVoyagerRecordId() {
    return voyagerRecordId;
  }

  @Override
  public Option<String> getStepPath() {
    return Option.of(stepPath);
  }

}
