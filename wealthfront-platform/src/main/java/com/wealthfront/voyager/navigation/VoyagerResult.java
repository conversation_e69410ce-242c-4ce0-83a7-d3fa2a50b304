package com.wealthfront.voyager.navigation;

import com.kaching.platform.common.Option;
import com.kaching.util.id.ExternalId;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.model.VoyagerType;
import com.wealthfront.voyager.ui.View;

public interface VoyagerResult<V extends View> {

  V getView();

  VoyagerStepId getStepId();

  ExternalId<VoyagerRecord> getVoyagerRecordId();

  Option<String> getStepPath();

  VoyagerType getVoyagerType();

}