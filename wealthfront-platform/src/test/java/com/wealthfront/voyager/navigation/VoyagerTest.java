package com.wealthfront.voyager.navigation;

import static com.kaching.platform.common.Option.some;
import static com.kaching.platform.queryengine.SoleOwnership.soleOwnership;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static com.wealthfront.voyager.example.ExampleMortgageApplication.State.PROCESSING;
import static com.wealthfront.voyager.example.navigation.MortgageRoute.MORTGAGE_TYPE_EXPIRED_1;
import static com.wealthfront.voyager.example.navigation.MortgageStepArguments.ApplicationArguments;
import static com.wealthfront.voyager.example.navigation.MortgageStepArguments.emptyArguments;
import static com.wealthfront.voyager.model.VoyagerRecord.State.ACTIVE;
import static com.wealthfront.voyager.model.VoyagerRecord.State.TERMINATED;
import static com.wealthfront.voyager.model.VoyagerRecordFactory.voyagerRecord;
import static com.wealthfront.voyager.model.VoyagerType.MORTGAGE_APPLICATION;
import static java.util.Collections.emptyList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Test;

import com.kaching.entities.ApiRequestMetadata;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.hibernate.ContentionSimulatingRetryingTransacter;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.Owned;
import com.kaching.platform.queryengine.Owner;
import com.kaching.platform.queryengine.OwnerExtractor;
import com.kaching.platform.queryengine.authorization.UserVerifier;
import com.kaching.platform.queryengine.exceptions.UnauthenticatedQueryException;
import com.kaching.platform.testing.WExpectations;
import com.kaching.user.UserId;
import com.kaching.user.UserPlatform;
import com.kaching.util.id.External;
import com.kaching.util.id.ExternalId;
import com.kaching.util.id.FlexId;
import com.kaching.util.id.IdExternalizer;
import com.wealthfront.voyager.VoyagerTestBase;
import com.wealthfront.voyager.example.ExampleMortgageApplication;
import com.wealthfront.voyager.example.navigation.MortgageRoute;
import com.wealthfront.voyager.example.navigation.MortgageStepArguments;
import com.wealthfront.voyager.example.payload.ExampleMortgageResult;
import com.wealthfront.voyager.example.payload.StateUpdateRequest;
import com.wealthfront.voyager.example.payload.StringUpdateRequest;
import com.wealthfront.voyager.example.steps.ExampleLandingStep;
import com.wealthfront.voyager.example.steps.SomeTerminalStep;
import com.wealthfront.voyager.example.steps.UserApplicationStep;
import com.wealthfront.voyager.example.views.ExampleAppUpgradeView;
import com.wealthfront.voyager.example.views.ExampleMortgageValidationErrorView;
import com.wealthfront.voyager.example.views.ExampleMortgageValidationErrorView.ExampleMortgageValidationError;
import com.wealthfront.voyager.example.views.ExampleStartView;
import com.wealthfront.voyager.example.views.ExampleSubflowAView;
import com.wealthfront.voyager.example.views.ExampleView;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.model.VoyagerType;

public class VoyagerTest extends VoyagerTestBase {

  protected IdExternalizer<VoyagerRecord> voyagerRecordIdExternalizer = mockery.mock(IdExternalizer.class);

  private static final DateTime NOW = new DateTime(2024, 12, 10, 0, 0, ET);
  private static final UserId authorizedUserId = new UserId(1L);

  private ApiRequestMetadata apiRequestMetadata = ApiRequestMetadata.with().build();
  private final VoyagerRouteExtractor routeExtractor = mockery.mock(VoyagerRouteExtractor.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void start() {
    UserId userId = new UserId(1L);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).externalizeId(with(any(Id.class)));
      will(returnValue(externalVoyagerRecordId));
    }});

    CreateVoyagerResult<ExampleMortgageResult> result = getVoyager()
        .start(userId, new ExampleLandingStep(), VoyagerStepId.of(MortgageRoute.LANDING, emptyArguments()));

    Id<VoyagerRecord> voyagerRecordId = transacter.executeWithReadOnlySessionExpression(session -> {
      VoyagerRecord voyagerRecord = session.createCriteria(VoyagerRecord.class).uniqueResult();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("application", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(ACTIVE, voyagerRecord.getState());
      assertEquals(userId, voyagerRecord.getUserId());
      return voyagerRecord.getId();
    });

    assertEquals(voyagerRecordId, result.id());
    assertEquals(VoyagerStepId.of("application"), result.result().getStepId());
    assertOptionEquals("application", result.result().getStepPath());
    assertEquals(externalVoyagerRecordId, result.result().getVoyagerRecordId());
    assertEquals(ExampleStartView.class, result.result().getView().getClass());
  }

  @Test
  public void start_authenticated() {
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).externalizeId(with(any(Id.class)));
      will(returnValue(externalVoyagerRecordId));
    }});

    setAuthorizedUserId(authorizedUserId);
    CreateVoyagerResult<ExampleMortgageResult> result = getVoyager()
        .start(authorizedUserId, new UserApplicationStep(), VoyagerStepId.of(MortgageRoute.USER_APPLICATION,
            new MortgageStepArguments.UserApplicationArguments(123L, new External<>(authorizedUserId.toString()))));

    Id<VoyagerRecord> voyagerRecordId = transacter.executeWithReadOnlySessionExpression(session -> {
      VoyagerRecord voyagerRecord = session.createCriteria(VoyagerRecord.class).uniqueResult();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("applications/123/user/1", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(ACTIVE, voyagerRecord.getState());
      assertEquals(authorizedUserId, voyagerRecord.getUserId());
      return voyagerRecord.getId();
    });

    assertEquals(voyagerRecordId, result.id());
    assertEquals(VoyagerStepId.of("applications/123/user/1"), result.result().getStepId());
    assertEquals("applications/{applicationId}/user/{userId}", result.result().getStepPath());
    assertEquals(externalVoyagerRecordId, result.result().getVoyagerRecordId());
    assertEquals(ExampleView.class, result.result().getView().getClass());
  }

  @Test
  public void start_unauthenticated() {
    UserId unauthorizedUserId = new UserId(2L);
    setAuthorizedUserId(authorizedUserId);
    assertThrows(UnauthenticatedQueryException.class, "Unauthorized access to owned resource in VoyagerStepArguments",
        () -> getVoyager().start(unauthorizedUserId, new UserApplicationStep(), VoyagerStepId.of(MortgageRoute.USER_APPLICATION,
            new MortgageStepArguments.UserApplicationArguments(123L, new External<>(unauthorizedUserId.toString())))));
  }

  @Test
  public void updateAndGetNext() {
    UserId userId = new UserId(1L);
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>(1);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/1/loan-type")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    VoyagerStepId stepId = VoyagerStepId.of("stepId");
    ExampleMortgageResult expectedResult = ExampleMortgageResult.builder().build();
    expectedResult.setStepIdForTesting(stepId);

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.LANDING, emptyArguments())));
    }});

    ExampleMortgageResult result = getVoyager()
        .updateAndGetNext(voyagerRecordFlexId, new StringUpdateRequest(stepId, "update"));

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("applications/5/loan-type", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(ACTIVE, voyagerRecord.getState());
      assertEquals(userId, voyagerRecord.getUserId());
    });

    assertEquals(ExampleView.class, result.getView().getClass());
    assertEquals(VoyagerStepId.of("applications/5/loan-type"), result.getStepId());
    assertEquals("applications/{applicationId}/loan-type", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
  }

  @Test
  public void updateAndGetNext_incompatible() {
    UserId userId = new UserId(1L);
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>(1);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/1/loan-type")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    VoyagerStepId stepId = VoyagerStepId.of("stepId");
    ExampleMortgageResult expectedResult = ExampleMortgageResult.builder().build();
    expectedResult.setStepIdForTesting(stepId);

    this.apiRequestMetadata = ApiRequestMetadata.with().userPlatform(UserPlatform.ANDROID).appVersion("999").build();
    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.LANDING, emptyArguments())));
    }});

    ExampleMortgageResult result = getVoyager()
        .updateAndGetNext(voyagerRecordFlexId, new StringUpdateRequest(stepId, "update"));

    assertEquals(ExampleAppUpgradeView.class, result.getView().getClass());
    assertEquals(VoyagerStepId.of("applications/5/loan-type"), result.getStepId());
    assertOptionEquals("applications/{applicationId}/loan-type", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
  }

  @Test
  public void updateAndGetNext_terminalStepIsNextStep() {
    UserId userId = new UserId(1L);
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>(1);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/5/journey-stage/123")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    VoyagerStepId stepId = VoyagerStepId.of("stepId");
    ExampleMortgageResult expectedResult = ExampleMortgageResult.builder().build();
    expectedResult.setStepIdForTesting(stepId);

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.MORTGAGE_JOURNEY, emptyArguments())));
    }});

    ExampleMortgageResult result = getVoyager()
        .updateAndGetNext(voyagerRecordFlexId, new StateUpdateRequest(stepId, PROCESSING));

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("applications/5/cancelled", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(TERMINATED, voyagerRecord.getState());
      assertEquals(userId, voyagerRecord.getUserId());
    });

    assertEquals(ExampleView.class, result.getView().getClass());
    assertEquals(VoyagerStepId.of("applications/5/cancelled"), result.getStepId());
    assertOptionEquals("applications/{applicationId}/cancelled", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
  }

  @Test
  public void updateAndGetNext_terminalStepIsNextStep_incompatible() {
    UserId userId = new UserId(1L);
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>(1);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/5/journey-stage/123")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    this.apiRequestMetadata = ApiRequestMetadata.with().userPlatform(UserPlatform.ANDROID).appVersion("999").build();

    VoyagerStepId stepId = VoyagerStepId.of("stepId");
    ExampleMortgageResult expectedResult = ExampleMortgageResult.builder().build();
    expectedResult.setStepIdForTesting(stepId);

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.MORTGAGE_JOURNEY, emptyArguments())));
    }});

    ExampleMortgageResult result = getVoyager()
        .updateAndGetNext(voyagerRecordFlexId, new StateUpdateRequest(stepId, PROCESSING));

    assertEquals(ExampleAppUpgradeView.class, result.getView().getClass());
    assertEquals(VoyagerStepId.of("applications/5/cancelled"), result.getStepId());
    assertOptionEquals("applications/{applicationId}/cancelled", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
  }

  @Test
  public void updateAndGetNext_attemptToGetNextOnTerminalStep() {
    UserId userId = new UserId(1L);
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>(1);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/1/cancelled")
        .withCreatedAt(NOW)
        .withState(TERMINATED)
        .buildAndPersist(transacter)
        .getId();

    VoyagerStepId stepId = VoyagerStepId.of("stepId");
    ExampleMortgageResult expectedResult = ExampleMortgageResult.builder().build();
    expectedResult.setStepIdForTesting(stepId);

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.APPLICATION_CANCELLED, emptyArguments())));
    }});

    assertThrows(IllegalStateException.class,
        "Terminal voyager steps do not have a next step.",
        () -> getVoyager().updateAndGetNext(voyagerRecordFlexId, new StringUpdateRequest(stepId, "update")));
  }

  @Test
  public void updateAndGetNext_getErrorView() {
    UserId userId = new UserId(1L);
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>(1);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/1/validation-error")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    VoyagerStepId stepId = VoyagerStepId.of("stepId");
    ExampleMortgageResult expectedResult = ExampleMortgageResult.builder().build();
    expectedResult.setStepIdForTesting(stepId);

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.ROUTE_WITH_VALIDATION_ERROR, emptyArguments())));
    }});

    ExampleMortgageResult result = getVoyager()
        .updateAndGetNext(voyagerRecordFlexId, new StateUpdateRequest(stepId, PROCESSING));

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("applications/1/validation-error", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(ACTIVE, voyagerRecord.getState());
      assertEquals(userId, voyagerRecord.getUserId());
    });

    ExampleMortgageValidationErrorView view = (ExampleMortgageValidationErrorView) result.getView();
    Map<String, String> errors = view.getMortgageValidationErrorsList().stream()
        .collect(Collectors.toMap(
            ExampleMortgageValidationError::stateId,
            ExampleMortgageValidationError::message));

    assertEquals(1, errors.size());
    assertEquals(result.getVoyagerRecordId(), externalVoyagerRecordId);
    assertEquals("example validation error message from view class", errors.get("badStateId"));
  }

  @Test
  public void updateAndGetNext_authenticated() {
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>(1);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(authorizedUserId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/123/user/1")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    VoyagerStepId stepId = VoyagerStepId.of("applications/123/user/1");
    ExampleMortgageResult expectedResult = ExampleMortgageResult.builder().build();
    expectedResult.setStepIdForTesting(stepId);

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.USER_APPLICATION, new MortgageStepArguments.UserApplicationArguments(
          123L, new External<>(authorizedUserId.toString())))));
    }});

    setAuthorizedUserId(authorizedUserId);
    ExampleMortgageResult result = getVoyager()
        .updateAndGetNext(voyagerRecordFlexId, new StateUpdateRequest(stepId, ExampleMortgageApplication.State.PROCESSING));

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("applications/5/journey-stage/2", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(ACTIVE, voyagerRecord.getState());
      assertEquals(authorizedUserId, voyagerRecord.getUserId());
    });

    assertEquals(ExampleSubflowAView.class, result.getView().getClass());
    assertEquals(VoyagerStepId.of("applications/5/journey-stage/2"), result.getStepId());
    assertOptionEquals("applications/{applicationId}/journey-stage/{borrowerId}", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
  }

  @Test
  public void updateAndGetNext_unauthenticated() {
    UserId unauthorizedUserId = new UserId(2L);
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>(1);
    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(unauthorizedUserId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/123/user/2")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    VoyagerStepId stepId = VoyagerStepId.of("applications/123/user/2");

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.USER_APPLICATION, new MortgageStepArguments.UserApplicationArguments(123L, new External<>(
          unauthorizedUserId.toString())))));
    }});

    setAuthorizedUserId(authorizedUserId);
    assertThrows(UnauthenticatedQueryException.class, "Unauthorized access to owned resource in VoyagerStepArguments",
        () -> getVoyager().updateAndGetNext(voyagerRecordFlexId, new StringUpdateRequest(stepId, "update")));
  }

  @Test
  public void resumeStep_activeStep() {
    UserId userId = new UserId(1L);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>("flex-id");

    VoyagerStepId stepId = VoyagerStepId.of("stepId");

    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/1/loan-type")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.LANDING, emptyArguments())));
    }});

    ExampleMortgageResult result = getVoyager().resumeStep(voyagerRecordFlexId, some(stepId));

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("applications/1/loan-type", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(ACTIVE, voyagerRecord.getState());
      assertEquals(userId, voyagerRecord.getUserId());
    });

    assertEquals(stepId, result.getStepId());
    assertOptionEquals("application", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
    assertEquals(ExampleStartView.class, result.getView().getClass());
  }

  @Test
  public void resumeStep_expiredStep() {
    UserId userId = new UserId(1);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>("flex-id");

    String invalidStepId = "application/5/loan-type";
    String targetStepId = "applications/5/loan-type";

    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(MORTGAGE_APPLICATION)
        .withCurrentStep(invalidStepId)
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter).getId();

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), VoyagerStepId.of(invalidStepId));
      will(returnValue(new Pair<>(MORTGAGE_TYPE_EXPIRED_1, new MortgageStepArguments.ApplicationArguments(1))));
    }});

    ExampleMortgageResult result = getVoyager().resumeStep(voyagerRecordFlexId, some(VoyagerStepId.of(invalidStepId)));

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals(targetStepId, voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(ACTIVE, voyagerRecord.getState());
      assertEquals(userId, voyagerRecord.getUserId());
    });

    assertEquals(targetStepId, result.getStepId().toString());
    assertOptionEquals("applications/{applicationId}/loan-type", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
    assertEquals(ExampleView.class, result.getView().getClass());
  }

  @Test
  public void resumeStep_terminatedVoyage() {
    UserId userId = new UserId(1L);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>("flex-id");

    String currentStep = "applications/1/cancelled";
    VoyagerStepId stepId = VoyagerStepId.of(currentStep);

    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/1/cancelled")
        .withCreatedAt(NOW)
        .withState(TERMINATED)
        .buildAndPersist(transacter)
        .getId();

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.APPLICATION_CANCELLED,
          new ApplicationArguments(1L))));
    }});

    ExampleMortgageResult result = getVoyager().resumeStep(voyagerRecordFlexId, some(stepId));

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals(currentStep, voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(TERMINATED, voyagerRecord.getState());
      assertEquals(userId, voyagerRecord.getUserId());
    });

    assertEquals(stepId, result.getStepId());
    assertOptionEquals("applications/{applicationId}/cancelled", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
    assertEquals(ExampleView.class, result.getView().getClass());
  }

  @Test
  public void resumeStep_authenticated() {
    UserId userId = new UserId(1L);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>("flex-id");

    VoyagerStepId stepId = VoyagerStepId.of("applications/123/user/1");

    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(authorizedUserId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/123/user/1")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.USER_APPLICATION, new MortgageStepArguments.UserApplicationArguments(123L, new External<>(authorizedUserId.toString())))));
    }});

    setAuthorizedUserId(authorizedUserId);
    ExampleMortgageResult result = getVoyager().resumeStep(voyagerRecordFlexId, some(stepId));

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("applications/123/user/1", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(ACTIVE, voyagerRecord.getState());
      assertEquals(userId, voyagerRecord.getUserId());
    });

    assertEquals(stepId, result.getStepId());
    assertOptionEquals("applications/{applicationId}/user/{userId}", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
    assertEquals(ExampleView.class, result.getView().getClass());
  }

  @Test
  public void resumeStep_unauthenticated() {
    UserId unauthorizedUserId = new UserId(2L);
    FlexId<VoyagerRecord> voyagerRecordFlexId = new FlexId<>(1);
    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(unauthorizedUserId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/123/user/2")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    VoyagerStepId stepId = VoyagerStepId.of("applications/123/user/2");

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).internalize(voyagerRecordFlexId);
      will(returnValue(voyagerRecordId));

      oneOf(routeExtractor).extractRouteAndArguments(MortgageRoute.values(), stepId);
      will(returnValue(new Pair<>(MortgageRoute.USER_APPLICATION, new MortgageStepArguments.UserApplicationArguments(123L, new External<>(unauthorizedUserId.toString())))));
    }});

    setAuthorizedUserId(authorizedUserId);
    assertThrows(UnauthenticatedQueryException.class, "Unauthorized access to owned resource in VoyagerStepArguments",
        () -> getVoyager().resumeStep(voyagerRecordFlexId, some(stepId)));
  }

  @Test
  public void navigateTo_navigableStep() {
    UserId userId = new UserId(1L);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    VoyagerStepId previousStepId = VoyagerStepId.of("previousStep");

    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep(previousStepId.getId())
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));
    }});

    ExampleLandingStep nextStep = new ExampleLandingStep();
    ExampleMortgageResult result = getVoyager().navigateTo(voyagerRecordId, nextStep, previousStepId);

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("application", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(ACTIVE, voyagerRecord.getState());
      assertEquals(userId, voyagerRecord.getUserId());
    });

    assertEquals(VoyagerStepId.of("application"), result.getStepId());
    assertOptionEquals("application", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
    assertEquals(ExampleStartView.class, result.getView().getClass());
  }

  @Test
  public void navigateTo_terminalStep() {
    UserId userId = new UserId(1L);
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    VoyagerStepId previousStepId = VoyagerStepId.of("previousStep");
    ApplicationArguments args = new ApplicationArguments(5L);

    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(userId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep(previousStepId.getId())
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));
    }});

    SomeTerminalStep nextStep = new SomeTerminalStep();
    ExampleMortgageResult result = getVoyager().navigateTo(voyagerRecordId, nextStep, previousStepId);

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("applications/5/cancelled", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(TERMINATED, voyagerRecord.getState());
      assertEquals(userId, voyagerRecord.getUserId());
    });

    assertEquals(VoyagerStepId.of(MortgageRoute.APPLICATION_CANCELLED, args), result.getStepId());
    assertOptionEquals("applications/{applicationId}/cancelled", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
    assertEquals(ExampleView.class, result.getView().getClass());
  }

  @Test
  public void navigateTo_authenticated() {
    ExternalId<VoyagerRecord> externalVoyagerRecordId = new ExternalId<>("external-id");
    VoyagerStepId previousStepId = VoyagerStepId.of("applications/123/user/1");

    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(authorizedUserId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep(previousStepId.getId())
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    mockery.checking(new WExpectations() {{
      oneOf(voyagerRecordIdExternalizer).externalizeId(voyagerRecordId);
      will(returnValue(externalVoyagerRecordId));
    }});

    setAuthorizedUserId(authorizedUserId);
    ExampleMortgageResult result = getVoyager().navigateTo(voyagerRecordId, new UserApplicationStep(), previousStepId);

    transacter.executeWithReadOnlySession(session -> {
      VoyagerRecord voyagerRecord = session.get(VoyagerRecord.class, voyagerRecordId).getOrThrow();
      assertEquals(VoyagerType.MORTGAGE_APPLICATION, voyagerRecord.getVoyagerType());
      assertEquals("applications/123/user/1", voyagerRecord.getCurrentStep().getId());
      assertEquals(NOW, voyagerRecord.getCreatedAt());
      assertEquals(ACTIVE, voyagerRecord.getState());
      assertEquals(authorizedUserId, voyagerRecord.getUserId());
    });

    assertEquals(VoyagerStepId.of("applications/123/user/1"), result.getStepId());
    assertOptionEquals("applications/{applicationId}/user/{userId}", result.getStepPath());
    assertEquals(externalVoyagerRecordId, result.getVoyagerRecordId());
    assertEquals(ExampleView.class, result.getView().getClass());
  }

  @Test
  public void navigateTo_unauthenticated() {
    UserId unauthorizedUserId = new UserId(2L);
    Id<VoyagerRecord> voyagerRecordId = voyagerRecord()
        .withUserId(unauthorizedUserId)
        .withVoyagerType(VoyagerType.MORTGAGE_APPLICATION)
        .withCurrentStep("applications/123/user/2")
        .withCreatedAt(NOW)
        .withState(ACTIVE)
        .buildAndPersist(transacter)
        .getId();

    VoyagerStepId previousStepId = VoyagerStepId.of("applications/123/user/2");

    setAuthorizedUserId(authorizedUserId);
    assertThrows(UnauthenticatedQueryException.class, "Unauthorized access to owned resource in VoyagerStepArguments",
        () -> getVoyager().navigateTo(voyagerRecordId, new UserApplicationStep(), previousStepId));
  }

  @Test
  public void isCompatibleWithStep() {
    ExampleLandingStep step = new ExampleLandingStep();

    ApiRequestMetadata iOSAppVersionCompatible = ApiRequestMetadata.with()
        .userPlatform(UserPlatform.IOS)
        .appVersion("2025.1.2")
        .build();
    ApiRequestMetadata iOSAppVersionExactlyCompatible = ApiRequestMetadata.with()
        .userPlatform(UserPlatform.IOS)
        .appVersion("2025.1.1")
        .build();
    ApiRequestMetadata iOSAppVersionVoyagerIncompatible = ApiRequestMetadata.with()
        .userPlatform(UserPlatform.IOS)
        .appVersion("2024.12.31")
        .build();

    ApiRequestMetadata androidAppVersionCompatible = ApiRequestMetadata.with()
        .userPlatform(UserPlatform.ANDROID)
        .appVersion("2001")
        .build();
    ApiRequestMetadata androidAppVersionExactlyCompatible = ApiRequestMetadata.with()
        .userPlatform(UserPlatform.ANDROID)
        .appVersion("2000")
        .build();
    ApiRequestMetadata androidAppVersionStepIncompatible = ApiRequestMetadata.with()
        .userPlatform(UserPlatform.ANDROID)
        .appVersion("1999")
        .build();

    this.apiRequestMetadata = iOSAppVersionCompatible;
    assertTrue(getVoyager().isCompatibleWithStep(step));

    this.apiRequestMetadata = iOSAppVersionExactlyCompatible;
    assertTrue(getVoyager().isCompatibleWithStep(step));

    this.apiRequestMetadata = iOSAppVersionVoyagerIncompatible;
    assertFalse(getVoyager().isCompatibleWithStep(step));

    this.apiRequestMetadata = androidAppVersionCompatible;
    assertTrue(getVoyager().isCompatibleWithStep(step));

    this.apiRequestMetadata = androidAppVersionExactlyCompatible;
    assertTrue(getVoyager().isCompatibleWithStep(step));

    this.apiRequestMetadata = androidAppVersionStepIncompatible;
    assertFalse(getVoyager().isCompatibleWithStep(step));
  }

  @Test
  public void isAuthorized_validAuthToken_returnsTrue() {
    setAuthorizedUserId(authorizedUserId);
    assertTrue(getVoyager().isAuthorized(new MortgageStepArguments.UserApplicationArguments(123L, new External<>(authorizedUserId.toString()))));
  }

  @Test
  public void isAuthorized_invalidAuthToken_returnsFalse() {
    UserId unauthorizedUserId = new UserId(2L);
    setAuthorizedUserId(authorizedUserId);
    assertFalse(getVoyager().isAuthorized(new MortgageStepArguments.UserApplicationArguments(123L, new External<>(unauthorizedUserId.toString()))));
  }

  @Test
  public void isAuthorized_ownedFieldIsNull_returnsFalse() {
    assertFalse(getVoyager().isAuthorized(new MortgageStepArguments.UserApplicationArguments(123L, null)));
  }

  @Test
  public void isAuthorized_nullArguments_returnsTrue() {
    assertTrue(getVoyager().isAuthorized(null));
  }

  @Test
  public void verifyOwnership_emptyList_returnsTrue() {
    assertTrue(getVoyager().verifyOwnership(emptyList()));
  }

  @Test
  public void verifyOwnership_allVerified_returnsTrue() {
    Voyager.OwnedFieldInfo info = new Voyager.OwnedFieldInfo(authorizedUserId,
        new Owned() {
          @Override
          public Class<? extends java.lang.annotation.Annotation> annotationType() {
            return Owned.class;
          }

          @Override
          public UserVerifier.Mode value() {
            return UserVerifier.Mode.ENFORCING;
          }
        },
        (OwnerExtractor<UserId>) (owned, session) -> soleOwnership(Owner.owner(owned)));
    setAuthorizedUserId(authorizedUserId);
    assertTrue(getVoyager().verifyOwnership(List.of(info)));
  }

  @Test
  public void verifyOwnership_fails_returnsFalse() {
    UserId unauthorizedUserId = new UserId(2L);
    Voyager.OwnedFieldInfo info = new Voyager.OwnedFieldInfo(unauthorizedUserId,
        new Owned() {
          @Override
          public Class<? extends java.lang.annotation.Annotation> annotationType() {
            return Owned.class;
          }

          @Override
          public UserVerifier.Mode value() {
            return UserVerifier.Mode.ENFORCING;
          }
        },
        (OwnerExtractor<UserId>) (owned, session) -> soleOwnership(Owner.owner(owned)));
    setAuthorizedUserId(authorizedUserId);
    assertFalse(getVoyager().verifyOwnership(List.of(info)));
  }

  @Test
  public void getOwnedFields_findsOwnedOnRecordComponent() {
    record TestRecordArgs(long id, @Owned External<UserId> userId) implements VoyagerStepArguments {}

    Map<String, Owned> ownedFields = getVoyager().getOwnedFields(TestRecordArgs.class);
    assertTrue(ownedFields.containsKey("userId"));
    assertNotNull(ownedFields.get("userId"));
  }

  private static class TestNonRecordArgs implements VoyagerStepArguments {
    private final External<UserId> userId;
    TestNonRecordArgs(@Owned External<UserId> userId) {
      this.userId = userId;
    }
  }

  @Test
  public void getOwnedFields_findsOwnedOnConstructorParameter() {
    Map<String, Owned> ownedFields = getVoyager().getOwnedFields(TestNonRecordArgs.class);
    assertTrue(ownedFields.containsKey("userId"));
    assertNotNull(ownedFields.get("userId"));
  }

  private static class MultiConstructorArgs implements VoyagerStepArguments {
    private final External<UserId> userId;
    private final External<UserId> coUserId;

    MultiConstructorArgs(@Owned External<UserId> userId) {
      this.userId = userId;
      this.coUserId = null;
    }

    MultiConstructorArgs(@Owned External<UserId> userId, @Owned External<UserId> coUserId) {
      this.userId = userId;
      this.coUserId = coUserId;
    }
  }

  @Test
  public void getOwnedFields_findsOwnedOnAllParamsInMultiConstructor() {
    Map<String, Owned> ownedFields = getVoyager().getOwnedFields(MultiConstructorArgs.class);
    assertTrue(ownedFields.containsKey("userId"));
    assertTrue(ownedFields.containsKey("coUserId"));
    assertNotNull(ownedFields.get("userId"));
    assertNotNull(ownedFields.get("coUserId"));
  }

  private static class NoOwnedArgs implements VoyagerStepArguments {
    private final External<UserId> userId;
    NoOwnedArgs(External<UserId> userId) {
      this.userId = userId;
    }
  }

  @Test
  public void getOwnedFields_returnsEmptyWhenNoOwned() {
    Map<String, Owned> ownedFields = getVoyager().getOwnedFields(NoOwnedArgs.class);
    assertTrue(ownedFields.isEmpty());
  }

  private static class MixedOwnedArgs implements VoyagerStepArguments {
    private final External<UserId> userId;
    private final String note;
    MixedOwnedArgs(@Owned External<UserId> userId, String note) {
      this.userId = userId;
      this.note = note;
    }
  }

  @Test
  public void getOwnedFields_findsOnlyOwnedParams() {
    Map<String, Owned> ownedFields = getVoyager().getOwnedFields(MixedOwnedArgs.class);
    assertTrue(ownedFields.containsKey("userId"));
    assertFalse(ownedFields.containsKey("note"));
  }

  private static class BaseArgs implements VoyagerStepArguments {
    private final External<UserId> userId;
    BaseArgs(@Owned External<UserId> userId) {
      this.userId = userId;
    }
  }

  private static class InheritedArgs extends BaseArgs {
    InheritedArgs(@Owned External<UserId> userId) {
      super(userId);
    }
  }

  @Test
  public void getOwnedFields_findsOwnedInInheritedConstructor() {
    Map<String, Owned> ownedFields = getVoyager().getOwnedFields(InheritedArgs.class);
    assertTrue(ownedFields.containsKey("userId"));
    assertNotNull(ownedFields.get("userId"));
  }

  private Voyager<MortgageRoute, ExampleMortgageResult> getVoyager() {
    ExampleVoyager voyager = new ExampleVoyager();
    voyager.transacter = new ContentionSimulatingRetryingTransacter(transacter);
    voyager.voyagerRecordIdExternalizer = voyagerRecordIdExternalizer;
    voyager.nowProvider = () -> NOW;
    voyager.apiRequestMetadataProvider = () -> Option.some(apiRequestMetadata);
    voyager.injector = injector();
    voyager.routeExtractor = routeExtractor;
    voyager.userVerifier = injector().getInstance(UserVerifier.class);
    return voyager;
  }

  private static class ExampleVoyager extends Voyager<MortgageRoute, ExampleMortgageResult> {

    @Override
    public VoyagerType getVoyagerType() {
      return VoyagerType.MORTGAGE_APPLICATION;
    }

    @Override
    public MortgageRoute[] getRoutes() {
      return MortgageRoute.values();
    }

    @Override
    public ExampleMortgageResult getErrorResult(VoyagerErrors voyagerErrors) {
      return ExampleMortgageResult.builder()
          .withView(new ExampleMortgageValidationErrorView())
          .build();
    }

    @Override
    public ExampleMortgageResult getIncompatibleResult() {
      return new ExampleMortgageResult(new ExampleAppUpgradeView());
    }

    @Override
    public Map<VoyagerRoute, VoyagerRoute> getExpiredRoutes() {
      return Map.of(MORTGAGE_TYPE_EXPIRED_1, MortgageRoute.MORTGAGE_SELECTION);
    }

    @Override
    public AppCompatibilityRequirement getCompatibilityRequirement() {
      return new AppCompatibilityRequirement(Option.some("2025.1.1"), Option.some("1000"));
    }

  }

}